# 开发规范和规则

- 游戏详情页面不应包含系统需求部分，包括最低配置和推荐配置的技术参数，保持页面简洁
- 游戏详情页面中关于特殊功能（如破解功能、作弊菜单等）的描述应该保持通用和模糊，不要详细列出具体功能，因为详细表达不一定与真相相符，应使用"具体功能请进入游戏后查看"等表述
- 新游戏条目应该添加到安卓游戏列表的最上面位置，而不是按拼音顺序排列
- 游戏页面中不应包含可能不准确的具体技术规格信息（如文件大小、系统版本要求等），因为这些信息可能与实际情况不符，应使用通用描述
- 为每一个资源页面的SEO做到最好是资源网站的核心策略，需要标准化SEO模板并应用到所有页面
- 创建任何资源页面前必须先搜索该资源的真实信息，包括游戏、软件、视频等所有类型的资源，不能仅凭名称推测内容，要基于搜索到的准确信息编写页面内容
- 创建资源页面前必须确保信息准确性，当搜索结果不确定或有多个可能时，应主动询问用户确认具体信息，不能基于不准确的搜索结果创建内容
- 应该主动识别工作中的重要经验教训并及时记录，不要等用户提醒才进行总结，要养成自主学习和改进的习惯
- Meta keywords标签在2024年SEO中已经不重要，Google完全忽略，百度权重极低，不需要作为SEO优化重点考虑
